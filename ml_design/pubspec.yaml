name: ml_design
description: "马良设计工具 - 类似Figma的协作设计工具Flutter前端"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI框架和图标
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # 状态管理
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # 网络请求
  dio: ^5.4.3+1
  retrofit: ^4.1.0
  json_annotation: ^4.9.0

  # WebSocket
  web_socket_channel: ^3.0.3

  # 本地存储
  sqflite: ^2.3.3+1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.3
  shared_preferences: ^2.2.3

  # 工具类
  uuid: ^4.4.0
  intl: ^0.20.2
  logger: ^2.3.0
  equatable: ^2.0.5

  # UI组件
  flutter_colorpicker: ^1.1.0
  file_picker: ^10.2.0
  image_picker: ^1.1.2

  # 画布和绘图
  flutter_svg: ^2.0.10+1
  vector_math: ^2.1.4

  # 跨平台支持
  desktop_window: ^0.4.0
  window_manager: ^0.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # 代码生成
  build_runner: ^2.4.9
  json_serializable: ^6.9.0
  retrofit_generator: ^8.2.1
  riverpod_generator: ^2.6.3

  # Hive代码生成
  hive_generator: ^2.0.1

  # 测试
  mockito: ^5.4.5

flutter:
  uses-material-design: true

  # 资源文件
  assets:
    - assets/images/
    - assets/icons/
