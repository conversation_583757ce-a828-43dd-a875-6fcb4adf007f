import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';
import '../utils/logger.dart';

/// 存储服务
class StorageService {
  static StorageService? _instance;
  SharedPreferences? _prefs;
  Box? _userSettingsBox;
  Box? _appSettingsBox;
  Box? _cacheBox;
  
  /// 获取单例实例
  static StorageService get instance {
    _instance ??= StorageService._internal();
    return _instance!;
  }
  
  StorageService._internal();
  
  /// 初始化存储服务
  Future<void> initialize() async {
    try {
      // 初始化SharedPreferences
      _prefs = await SharedPreferences.getInstance();

      // 初始化Hive
      await Hive.initFlutter();

      // 尝试打开Hive boxes，如果失败则重试
      await _openHiveBoxesWithRetry();

      AppLogger.info('存储服务初始化成功');
    } catch (e, stackTrace) {
      AppLogger.error('存储服务初始化失败', e, stackTrace);
      // 即使Hive初始化失败，也不要阻止应用启动
      // 只使用SharedPreferences作为后备方案
      AppLogger.warning('使用SharedPreferences作为后备存储方案');
    }
  }

  /// 尝试打开Hive boxes，带重试机制
  Future<void> _openHiveBoxesWithRetry() async {
    const maxRetries = 3;
    const retryDelay = Duration(milliseconds: 500);

    for (int i = 0; i < maxRetries; i++) {
      try {
        _userSettingsBox = await Hive.openBox(HiveBoxNames.userSettings);
        _appSettingsBox = await Hive.openBox(HiveBoxNames.appSettings);
        _cacheBox = await Hive.openBox(HiveBoxNames.cache);
        return; // 成功打开，退出重试循环
      } catch (e) {
        AppLogger.warning('Hive boxes打开失败，尝试 ${i + 1}/$maxRetries: $e');

        if (i < maxRetries - 1) {
          await Future.delayed(retryDelay);

          // 尝试关闭可能已经打开的boxes
          try {
            await _userSettingsBox?.close();
            await _appSettingsBox?.close();
            await _cacheBox?.close();
          } catch (_) {}

          _userSettingsBox = null;
          _appSettingsBox = null;
          _cacheBox = null;
        } else {
          rethrow; // 最后一次重试失败，抛出异常
        }
      }
    }
  }
  
  /// 确保SharedPreferences已初始化
  Future<SharedPreferences> get prefs async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }
  
  /// 确保Hive boxes已初始化
  Future<void> _ensureHiveInitialized() async {
    if (_userSettingsBox == null ||
        _appSettingsBox == null ||
        _cacheBox == null) {
      try {
        await _openHiveBoxesWithRetry();
      } catch (e) {
        AppLogger.warning('Hive boxes初始化失败，将使用SharedPreferences: $e');
      }
    }
  }

  /// 检查Hive是否可用
  bool get _isHiveAvailable =>
      _userSettingsBox != null &&
      _appSettingsBox != null &&
      _cacheBox != null;
  
  // ==================== 用户相关存储 ====================
  
  /// 保存用户Token
  Future<void> saveUserToken(String token) async {
    final preferences = await prefs;
    await preferences.setString(StorageKeys.userToken, token);
    AppLogger.info('用户Token已保存');
  }
  
  /// 获取用户Token
  Future<String?> getUserToken() async {
    final preferences = await prefs;
    return preferences.getString(StorageKeys.userToken);
  }
  
  /// 清除用户Token
  Future<void> clearUserToken() async {
    final preferences = await prefs;
    await preferences.remove(StorageKeys.userToken);
    AppLogger.info('用户Token已清除');
  }
  
  /// 保存用户信息
  Future<void> saveUserInfo(User user) async {
    final preferences = await prefs;
    final userJson = jsonEncode(user.toJson());
    await preferences.setString(StorageKeys.userInfo, userJson);
    AppLogger.info('用户信息已保存');
  }
  
  /// 获取用户信息
  Future<User?> getUserInfo() async {
    try {
      final preferences = await prefs;
      final userJson = preferences.getString(StorageKeys.userInfo);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      }
    } catch (e, stackTrace) {
      AppLogger.error('获取用户信息失败', e, stackTrace);
    }
    return null;
  }
  
  /// 清除用户信息
  Future<void> clearUserInfo() async {
    final preferences = await prefs;
    await preferences.remove(StorageKeys.userInfo);
    AppLogger.info('用户信息已清除');
  }
  
  // ==================== 应用设置存储 ====================
  
  /// 保存语言设置
  Future<void> saveLanguage(String languageCode) async {
    final preferences = await prefs;
    await preferences.setString(StorageKeys.language, languageCode);
  }
  
  /// 获取语言设置
  Future<String?> getLanguage() async {
    final preferences = await prefs;
    return preferences.getString(StorageKeys.language);
  }
  
  /// 保存主题设置
  Future<void> saveTheme(String theme) async {
    final preferences = await prefs;
    await preferences.setString(StorageKeys.theme, theme);
  }
  
  /// 获取主题设置
  Future<String?> getTheme() async {
    final preferences = await prefs;
    return preferences.getString(StorageKeys.theme);
  }
  
  /// 保存最近打开的项目
  Future<void> saveRecentProjects(List<int> projectIds) async {
    final preferences = await prefs;
    final projectIdsJson = jsonEncode(projectIds);
    await preferences.setString(StorageKeys.recentProjects, projectIdsJson);
  }
  
  /// 获取最近打开的项目
  Future<List<int>> getRecentProjects() async {
    try {
      final preferences = await prefs;
      final projectIdsJson = preferences.getString(StorageKeys.recentProjects);
      if (projectIdsJson != null) {
        final projectIds = jsonDecode(projectIdsJson) as List;
        return projectIds.cast<int>();
      }
    } catch (e, stackTrace) {
      AppLogger.error('获取最近项目失败', e, stackTrace);
    }
    return [];
  }
  
  // ==================== Hive存储 ====================
  
  /// 保存用户设置到Hive
  Future<void> saveUserSetting(String key, dynamic value) async {
    await _ensureHiveInitialized();
    if (_isHiveAvailable) {
      await _userSettingsBox!.put(key, value);
    } else {
      // 使用SharedPreferences作为后备
      final prefs = await this.prefs;
      if (value is String) {
        await prefs.setString('user_$key', value);
      } else if (value is int) {
        await prefs.setInt('user_$key', value);
      } else if (value is bool) {
        await prefs.setBool('user_$key', value);
      } else if (value is double) {
        await prefs.setDouble('user_$key', value);
      }
    }
  }

  /// 从Hive获取用户设置
  Future<T?> getUserSetting<T>(String key) async {
    await _ensureHiveInitialized();
    if (_isHiveAvailable) {
      return _userSettingsBox!.get(key) as T?;
    } else {
      // 使用SharedPreferences作为后备
      final prefs = await this.prefs;
      return prefs.get('user_$key') as T?;
    }
  }

  /// 保存应用设置到Hive
  Future<void> saveAppSetting(String key, dynamic value) async {
    await _ensureHiveInitialized();
    if (_isHiveAvailable) {
      await _appSettingsBox!.put(key, value);
    } else {
      // 使用SharedPreferences作为后备
      final prefs = await this.prefs;
      if (value is String) {
        await prefs.setString('app_$key', value);
      } else if (value is int) {
        await prefs.setInt('app_$key', value);
      } else if (value is bool) {
        await prefs.setBool('app_$key', value);
      } else if (value is double) {
        await prefs.setDouble('app_$key', value);
      }
    }
  }

  /// 从Hive获取应用设置
  Future<T?> getAppSetting<T>(String key) async {
    await _ensureHiveInitialized();
    if (_isHiveAvailable) {
      return _appSettingsBox!.get(key) as T?;
    } else {
      // 使用SharedPreferences作为后备
      final prefs = await this.prefs;
      return prefs.get('app_$key') as T?;
    }
  }
  
  /// 保存缓存数据到Hive
  Future<void> saveCache(String key, dynamic value, {Duration? expiry}) async {
    await _ensureHiveInitialized();
    if (_isHiveAvailable) {
      final cacheData = {
        'value': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiry': expiry?.inMilliseconds,
      };
      await _cacheBox!.put(key, cacheData);
    } else {
      // 缓存功能在Hive不可用时暂时禁用
      AppLogger.warning('缓存功能不可用，Hive未初始化');
    }
  }

  /// 从Hive获取缓存数据
  Future<T?> getCache<T>(String key) async {
    await _ensureHiveInitialized();
    if (_isHiveAvailable) {
      final cacheData = _cacheBox!.get(key) as Map?;
      if (cacheData == null) return null;

      final timestamp = cacheData['timestamp'] as int;
      final expiry = cacheData['expiry'] as int?;

      // 检查是否过期
      if (expiry != null) {
        final expiryTime = timestamp + expiry;
        if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
          await _cacheBox!.delete(key);
          return null;
        }
      }

      return cacheData['value'] as T?;
    }
    return null;
  }

  /// 清除所有缓存
  Future<void> clearCache() async {
    await _ensureHiveInitialized();
    if (_isHiveAvailable) {
      await _cacheBox!.clear();
      AppLogger.info('缓存已清除');
    }
  }
  
  /// 清除所有用户数据
  Future<void> clearAllUserData() async {
    await clearUserToken();
    await clearUserInfo();
    await _ensureHiveInitialized();
    await _userSettingsBox!.clear();
    AppLogger.info('所有用户数据已清除');
  }
  
  /// 关闭存储服务
  Future<void> close() async {
    await _userSettingsBox?.close();
    await _appSettingsBox?.close();
    await _cacheBox?.close();
    AppLogger.info('存储服务已关闭');
  }
}
