import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../constants/api_constants.dart';
import '../utils/logger.dart';

/// 认证状态
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// 认证状态类
class AuthState {
  final AuthStatus status;
  final User? user;
  final String? token;
  final String? errorMessage;

  const AuthState({
    required this.status,
    this.user,
    this.token,
    this.errorMessage,
  });

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? token,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      token: token ?? this.token,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  bool get isAuthenticated => status == AuthStatus.authenticated;
  bool get isLoading => status == AuthStatus.loading;
  bool get hasError => status == AuthStatus.error;
}

/// 认证状态管理器
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService = AuthService();

  AuthNotifier() : super(const AuthState(status: AuthStatus.initial)) {
    _initializeAuth();
  }

  /// 初始化认证状态
  Future<void> _initializeAuth() async {
    try {
      state = state.copyWith(status: AuthStatus.loading);
      
      await _authService.initializeAuth();
      final isLoggedIn = await _authService.isLoggedIn();
      
      if (isLoggedIn) {
        final user = await _authService.getLocalUserInfo();
        final token = await _authService.getLocalToken();
        
        state = state.copyWith(
          status: AuthStatus.authenticated,
          user: user,
          token: token,
        );
        
        AppLogger.info('用户已登录: ${user?.username}');
      } else {
        state = state.copyWith(status: AuthStatus.unauthenticated);
      }
    } catch (e, stackTrace) {
      AppLogger.error('初始化认证状态失败', e, stackTrace);
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: '初始化失败: $e',
      );
    }
  }

  /// 用户注册
  Future<bool> register(RegisterRequest request) async {
    try {
      state = state.copyWith(status: AuthStatus.loading);
      
      final user = await _authService.register(request);
      
      AppLogger.userAction('用户注册', parameters: {
        'username': request.username,
        'email': request.email,
      });
      
      // 注册成功后自动登录
      final loginRequest = LoginRequest(
        username: request.username,
        password: request.password,
      );
      
      return await login(loginRequest);
    } catch (e, stackTrace) {
      AppLogger.error('用户注册失败', e, stackTrace);
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e),
      );
      return false;
    }
  }

  /// 用户登录
  Future<bool> login(LoginRequest request) async {
    try {
      state = state.copyWith(status: AuthStatus.loading);
      
      final loginResponse = await _authService.login(request);
      
      state = state.copyWith(
        status: AuthStatus.authenticated,
        user: loginResponse.userInfo,
        token: loginResponse.token,
        errorMessage: null,
      );
      
      AppLogger.userAction('用户登录', parameters: {
        'username': request.username,
        'userId': loginResponse.userInfo.id,
      });
      
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('用户登录失败', e, stackTrace);
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e),
      );
      return false;
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      await _authService.logout();
      
      state = const AuthState(status: AuthStatus.unauthenticated);
      
      AppLogger.userAction('用户登出');
    } catch (e, stackTrace) {
      AppLogger.error('用户登出失败', e, stackTrace);
      // 即使登出失败，也要清除本地状态
      state = const AuthState(status: AuthStatus.unauthenticated);
    }
  }

  /// 刷新用户信息
  Future<void> refreshUserInfo() async {
    if (!state.isAuthenticated) return;
    
    try {
      final user = await _authService.getCurrentUser();
      state = state.copyWith(user: user);
      
      AppLogger.info('用户信息已刷新');
    } catch (e, stackTrace) {
      AppLogger.error('刷新用户信息失败', e, stackTrace);
    }
  }

  /// 刷新Token
  Future<void> refreshToken() async {
    if (!state.isAuthenticated) return;
    
    try {
      await _authService.refreshToken();
      AppLogger.info('Token已刷新');
    } catch (e, stackTrace) {
      AppLogger.error('刷新Token失败', e, stackTrace);
      // Token刷新失败，可能需要重新登录
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Token已过期，请重新登录',
      );
    }
  }

  /// 清除错误状态
  void clearError() {
    if (state.hasError) {
      state = state.copyWith(
        status: state.user != null ? AuthStatus.authenticated : AuthStatus.unauthenticated,
        errorMessage: null,
      );
    }
  }

  /// 获取错误信息
  String _getErrorMessage(dynamic error) {
    if (error is ApiException) {
      switch (error.code) {
        case BusinessErrorCodes.usernameExists:
          return '用户名已存在';
        case BusinessErrorCodes.emailExists:
          return '邮箱已存在';
        case BusinessErrorCodes.invalidCredentials:
          return '用户名或密码错误';
        case BusinessErrorCodes.userDisabled:
          return '账号已被禁用';
        default:
          return error.message;
      }
    } else if (error is NetworkException) {
      return '网络连接失败，请检查网络设置';
    } else {
      return '操作失败: $error';
    }
  }
}

/// 认证状态Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

/// 当前用户Provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});

/// 是否已登录Provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.isAuthenticated;
});
