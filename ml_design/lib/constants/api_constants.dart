/// API相关常量
class ApiConstants {
  /// 基础URL
  static const String baseUrl = 'http://localhost:8080';
  
  /// API版本
  static const String apiVersion = 'v1.0.0';
  
  /// WebSocket URL
  static const String wsBaseUrl = 'ws://localhost:8080';
  
  /// 请求超时时间（毫秒）
  static const int connectTimeout = 30000;
  static const int receiveTimeout = 30000;
  static const int sendTimeout = 30000;
  
  /// Token相关
  static const String authHeaderName = 'Authorization';
  static const int tokenValidityDays = 30;
  
  /// 分页默认值
  static const int defaultPageSize = 10;
  static const int maxPageSize = 100;
  
  /// 画布默认值
  static const int defaultCanvasWidth = 1920;
  static const int defaultCanvasHeight = 1080;
  static const String defaultBackgroundColor = '#FFFFFF';
  
  /// 元素默认值
  static const double defaultElementWidth = 100.0;
  static const double defaultElementHeight = 100.0;
  static const double defaultOpacity = 1.0;
  static const double defaultRotation = 0.0;
  static const int defaultZIndex = 0;
}

/// API端点路径
class ApiPaths {
  // 认证相关
  static const String register = '/api/auth/register';
  static const String login = '/api/auth/login';
  static const String logout = '/api/auth/logout';
  static const String me = '/api/auth/me';
  static const String refreshToken = '/api/auth/refresh';
  
  // 项目相关
  static const String projects = '/api/projects';
  static String projectDetail(int projectId) => '/api/projects/$projectId';
  static String projectCanvases(int projectId) => '/api/projects/$projectId/canvases';
  
  // 画布相关
  static String canvasDetail(int canvasId) => '/api/canvases/$canvasId';
  static String updateCanvasData(int canvasId) => '/api/canvases/$canvasId/data';
  static String canvasElements(int canvasId) => '/api/canvases/$canvasId/elements';
  
  // 设计元素相关
  static String elementDetail(int elementId) => '/api/elements/$elementId';
  
  // 健康检查
  static const String health = '/api/health';
  static const String healthDetailed = '/api/health/detailed';
  
  // 测试接口
  static const String testHello = '/api/test/hello';
  static const String testStatus = '/api/test/status';
  
  // MCP Server相关
  static const String mcpInfo = '/mcp/info';
  static const String mcpResources = '/mcp/resources';
  static const String mcpResourcesRead = '/mcp/resources/read';
  static const String mcpTools = '/mcp/tools';
  static const String mcpToolsCall = '/mcp/tools/call';
  
  // WebSocket
  static String wsCollaboration(int projectId) => '/ws/collaboration/$projectId';
}

/// HTTP状态码
class HttpStatusCodes {
  static const int ok = 200;
  static const int badRequest = 400;
  static const int unauthorized = 401;
  static const int forbidden = 403;
  static const int notFound = 404;
  static const int internalServerError = 500;
}

/// 业务错误码
class BusinessErrorCodes {
  // 用户相关错误
  static const int usernameExists = 1001;
  static const int emailExists = 1002;
  static const int invalidCredentials = 1003;
  static const int userDisabled = 1004;

  // 项目相关错误
  static const int projectNotFound = 2001;
  static const int projectAccessDenied = 2002;

  // 画布相关错误
  static const int canvasNotFound = 3001;
  static const int canvasDataFormatError = 3002;

  // 设计元素相关错误
  static const int elementNotFound = 4001;
  static const int elementTypeNotSupported = 4002;
}
