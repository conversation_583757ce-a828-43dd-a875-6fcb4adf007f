import 'package:flutter/material.dart';

/// 应用常量
class AppConstants {
  /// 应用名称
  static const String appName = '马良设计工具';
  
  /// 应用版本
  static const String appVersion = '1.0.0';
  
  /// 应用描述
  static const String appDescription = '类似Figma的协作设计工具';
  
  /// 支持的语言
  static const List<Locale> supportedLocales = [
    Locale('zh', 'CN'), // 中文
    Locale('en', 'US'), // 英文
  ];
  
  /// 默认语言
  static const Locale defaultLocale = Locale('zh', 'CN');
}

/// 存储键名
class StorageKeys {
  /// 用户Token
  static const String userToken = 'user_token';
  
  /// 用户信息
  static const String userInfo = 'user_info';
  
  /// 语言设置
  static const String language = 'language';
  
  /// 主题设置
  static const String theme = 'theme';
  
  /// 最近打开的项目
  static const String recentProjects = 'recent_projects';
  
  /// 画布设置
  static const String canvasSettings = 'canvas_settings';
  
  /// 工具栏设置
  static const String toolbarSettings = 'toolbar_settings';
}

/// 数据库相关常量
class DatabaseConstants {
  /// 数据库名称
  static const String databaseName = 'ml_design.db';
  
  /// 数据库版本
  static const int databaseVersion = 1;
  
  /// 表名
  static const String userTable = 'users';
  static const String projectTable = 'projects';
  static const String canvasTable = 'canvases';
  static const String elementTable = 'elements';
  static const String cacheTable = 'cache';
}

/// Hive Box名称
class HiveBoxNames {
  /// 用户设置
  static const String userSettings = 'user_settings';
  
  /// 应用设置
  static const String appSettings = 'app_settings';
  
  /// 缓存数据
  static const String cache = 'cache';
  
  /// 离线数据
  static const String offline = 'offline';
}

/// 动画持续时间
class AnimationDurations {
  /// 快速动画
  static const Duration fast = Duration(milliseconds: 150);
  
  /// 正常动画
  static const Duration normal = Duration(milliseconds: 300);
  
  /// 慢速动画
  static const Duration slow = Duration(milliseconds: 500);
  
  /// 页面转场动画
  static const Duration pageTransition = Duration(milliseconds: 250);
}

/// 尺寸常量
class Dimensions {
  /// 边距
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  /// 圆角
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  
  /// 图标尺寸
  static const double iconS = 16.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  
  /// 按钮高度
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  
  /// 工具栏
  static const double toolbarHeight = 56.0;
  static const double sidebarWidth = 280.0;
  static const double panelWidth = 320.0;
  
  /// 画布
  static const double canvasMinZoom = 0.1;
  static const double canvasMaxZoom = 10.0;
  static const double canvasDefaultZoom = 1.0;
}

/// 颜色常量
class AppColors {
  /// 主色调
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryDark = Color(0xFF1976D2);
  static const Color primaryLight = Color(0xFFBBDEFB);
  
  /// 辅助色
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryDark = Color(0xFFF57C00);
  static const Color secondaryLight = Color(0xFFFFE0B2);
  
  /// 状态色
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  /// 中性色
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);
  
  /// 画布相关
  static const Color canvasBackground = Color(0xFFF8F9FA);
  static const Color canvasGrid = Color(0xFFE1E5E9);
  static const Color selectionBorder = Color(0xFF2196F3);
  static const Color hoverBorder = Color(0xFF90CAF9);
}

/// 字体常量
class FontConstants {
  /// 字体家族
  static const String fontFamily = 'Roboto';
  
  /// 字体大小
  static const double fontSizeXS = 10.0;
  static const double fontSizeS = 12.0;
  static const double fontSizeM = 14.0;
  static const double fontSizeL = 16.0;
  static const double fontSizeXL = 18.0;
  static const double fontSizeXXL = 20.0;
  
  /// 标题字体大小
  static const double titleS = 16.0;
  static const double titleM = 20.0;
  static const double titleL = 24.0;
  static const double titleXL = 28.0;
  
  /// 字体权重
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
}
